import { Metadata } from 'next';
import { Locale } from '../../../lib/i18n';
import { generateCategoriesMetadata } from '../../../lib/metadata';
import CategoriesPage from '../../../components/CategoriesPage';

interface PageProps {
  params: Promise<{
    locale: Locale;
  }>;
}

export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
  const { locale } = await params;
  return generateCategoriesMetadata(locale);
}

export default async function Categories({ params }: PageProps) {
  const { locale } = await params;

  return <CategoriesPage locale={locale} />;
}
